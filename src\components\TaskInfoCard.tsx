import React, { useState } from 'react';
import {
  FileText,
  ExternalLink,
  Clock,
  Copy,
  Trash2
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { renderMarkdown } from '@/utils/markdown';

interface GeneratedContent {
  id: string;
  content: string;
  target_platform: string;
}

interface TaskInfo {
  task_id: string;
  created_at: string;
  generated_content?: GeneratedContent[];
  summary_info?: {
    title?: string;
    url?: string;
    source_name?: string;
  };
}

interface TaskInfoCardProps {
  task: TaskInfo;
  onCopy?: (content: string) => void;
  onDelete?: (contentId: string, taskId: string) => void;
  isExpanded?: boolean;
}

const TaskInfoCard: React.FC<TaskInfoCardProps> = ({ task, onCopy, onDelete, isExpanded = false }) => {
  const { t } = useTranslation();

  console.log('TaskInfoCard render:', {
    taskId: task.task_id,
    isExpanded,
    hasGeneratedContent: !!task.generated_content,
    generatedContentLength: task.generated_content?.length || 0
  });

  const getPlatformName = (platform: string) => {
    const platformMap: { [key: string]: string } = {
      'rednote': t('platforms.rednote', 'Rednote'),
      'twitter': t('platforms.twitter', 'Twitter'),
      'linkedin': t('platforms.linkedin', 'LinkedIn'),
      'wechat': t('platforms.wechat', 'WeChat'),
      'blog': t('platforms.blog', 'Blog'),
      'podcast': t('platforms.podcast', 'Podcast')
    };
    return platformMap[platform] || platform;
  };

  // 如果没有生成内容，不显示任何内容
  if (!task.generated_content || task.generated_content.length === 0) {
    return null;
  }

  return (
    <div>
      {/* 展开状态：Generated Content */}
      {isExpanded && (
        <div className="mt-6 pt-6 border-t border-border/50">
          <div className="mb-4">
            <h3 className="text-sm font-semibold text-foreground flex items-center gap-2">
              <FileText className="h-4 w-4 text-primary" />
              {t('contentHistory.labels.generatedContent', 'Generated Content')}
            </h3>
          </div>
          <div className="space-y-4">
            {task.generated_content.map((content) => (
              <div key={content.id} className="bg-muted/30 border border-border/30 rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300">
                <div className="flex items-center justify-between mb-3">
                  <Badge variant="outline" className="text-xs bg-primary/10 text-primary border-primary/30 hover:bg-primary/20 transition-colors duration-300">
                    {getPlatformName(content.target_platform)}
                  </Badge>
                  <div className="flex items-center gap-2">
                    {onCopy && (
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          onCopy(content.content);
                        }}
                        variant="outline"
                        size="sm"
                        className="border-primary/30 text-primary hover:bg-primary/10 hover:border-primary/50 transition-all duration-300"
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        {t('contentHistory.actions.copy', 'Copy')}
                      </Button>
                    )}
                    {onDelete && (
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          onDelete(content.id, task.task_id);
                        }}
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 hover:border-red-300 transition-all duration-300"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        {t('contentHistory.actions.delete', 'Delete')}
                      </Button>
                    )}
                  </div>
                </div>
                <div
                  className="text-sm leading-relaxed max-w-none markdown-content p-3 bg-background/50 rounded-lg border border-border/30"
                  dangerouslySetInnerHTML={{
                    __html: renderMarkdown(content.content)
                  }}
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskInfoCard;
