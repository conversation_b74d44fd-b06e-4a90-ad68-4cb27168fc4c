import React from 'react';
import {
  <PERSON><PERSON><PERSON>t,
  ExternalLink,
  Clock,
  CheckCircle,
  XCircle,
  <PERSON>use,
  AlertCircle,
  <PERSON><PERSON>,
  Trash2
} from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { renderMarkdown } from '@/utils/markdown';

interface GeneratedContent {
  id: string;
  content: string;
  target_platform: string;
}

interface TaskInfo {
  id: string;
  status: 'completed' | 'failed' | 'pending' | 'processing';
  style: string;
  target_platforms: string[];
  user_input?: string;
  completed_at?: string;
  created_at: string;
  started_at?: string;
  error_message?: string;
  retry_count?: number;
  max_retries?: number;
  queue_position?: number;
  generated_content?: GeneratedContent[];
  summary_info?: {
    title?: string;
    url?: string;
    source_name?: string;
    platform?: string;
    topic_name?: string;
    created_at?: string;
    source_urls?: string[];
  };
}

interface TaskInfoCardProps {
  task: TaskInfo;
  onCopy?: (content: string) => void;
  onDelete?: (contentId: string, taskId: string) => void;
}

const TaskInfoCard: React.FC<TaskInfoCardProps> = ({ task, onCopy, onDelete }) => {
  const { t } = useTranslation();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'processing':
        return <Clock className="h-4 w-4 text-blue-600 animate-spin" />;
      case 'pending':
        return <Pause className="h-4 w-4 text-yellow-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return t('contentHistory.status.completed', 'Completed');
      case 'failed':
        return t('contentHistory.status.failed', 'Failed');
      case 'processing':
        return t('contentHistory.status.processing', 'Processing');
      case 'pending':
        return t('contentHistory.status.pending', 'Pending');
      default:
        return t('contentHistory.status.unknown', 'Unknown');
    }
  };

  const getStyleName = (style: string) => {
    const styleMap: { [key: string]: string } = {
      'engaging': t('contentHistory.styles.engaging', 'Engaging'),
      'professional': t('contentHistory.styles.professional', 'Professional'),
      'casual': t('contentHistory.styles.casual', 'Casual'),
      'technical': t('contentHistory.styles.technical', 'Technical')
    };
    return styleMap[style] || style;
  };

  const getPlatformName = (platform: string) => {
    const platformMap: { [key: string]: string } = {
      'rednote': t('platforms.rednote', 'Rednote'),
      'twitter': t('platforms.twitter', 'Twitter'),
      'linkedin': t('platforms.linkedin', 'LinkedIn'),
      'wechat': t('platforms.wechat', 'WeChat'),
      'blog': t('platforms.blog', 'Blog'),
      'podcast': t('platforms.podcast', 'Podcast')
    };
    return platformMap[platform] || platform;
  };

  return (
    <div className="space-y-6">
      {/* Generation Information Section */}
      <Card className="bg-gradient-to-br from-blue-50 to-blue-100/50 border border-blue-200/50 rounded-xl shadow-card hover:shadow-glow transition-all duration-300">
        <CardHeader className="pb-4">
          <CardTitle className="text-sm font-semibold text-blue-900 flex items-center gap-2">
            <div className="p-1 bg-blue-100 rounded-lg">
              <FileText className="h-4 w-4 text-blue-600" />
            </div>
            {t('contentHistory.labels.generationInfo', 'Generation Information')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Task Status and Target Platform */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="text-sm font-medium text-blue-800">
                {t('contentHistory.labels.taskStatus', 'Task Status')}
              </div>
              <div className="flex items-center gap-2 p-3 bg-white/50 rounded-lg border border-blue-100">
                {getStatusIcon(task.status)}
                <span className="text-sm font-medium">{getStatusText(task.status)}</span>
                {task.queue_position && (
                  <span className="text-xs text-muted-foreground ml-auto">
                    ({t('contentHistory.labels.queuePosition', 'Queue')}: {task.queue_position})
                  </span>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <div className="text-sm font-medium text-blue-800">
                {t('contentHistory.labels.targetPlatform', 'Target Platform')}
              </div>
              <div className="flex flex-wrap gap-2 p-3 bg-white/50 rounded-lg border border-blue-100">
                {task.target_platforms.map((platform) => (
                  <Badge key={platform} variant="outline" className="text-xs bg-primary/10 text-primary border-primary/30">
                    {getPlatformName(platform)}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Generation Style and User Input */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="text-sm font-medium text-blue-800">
                {t('contentHistory.labels.generationStyle', 'Generation Style')}
              </div>
              <div className="p-3 bg-white/50 rounded-lg border border-blue-100">
                <Badge variant="secondary" className="bg-blue-100 text-blue-700 border-blue-200">
                  {getStyleName(task.style)}
                </Badge>
              </div>
            </div>
            {task.user_input && (
              <div className="space-y-2">
                <div className="text-sm font-medium text-blue-800">
                  {t('contentHistory.labels.userIdea', 'User Input')}
                </div>
                <div className="p-3 bg-white/50 rounded-lg border border-blue-100 text-sm text-foreground">
                  {task.user_input}
                </div>
              </div>
            )}
          </div>

          {/* Completion Time */}
          {task.completed_at && (
            <div className="space-y-2">
              <div className="text-sm font-medium text-blue-800">
                {t('contentHistory.labels.completionTime', 'Completion Time')}
              </div>
              <div className="p-3 bg-white/50 rounded-lg border border-blue-100 text-sm text-foreground flex items-center gap-2">
                <Clock className="h-4 w-4 text-blue-600" />
                {new Date(task.completed_at).toLocaleString()}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Data Source Information Section */}
      {task.summary_info && (
        <Card className="bg-gradient-to-br from-green-50 to-green-100/50 border border-green-200/50 rounded-xl shadow-card hover:shadow-glow transition-all duration-300">
          <CardHeader className="pb-4">
            <CardTitle className="text-sm font-semibold text-green-900 flex items-center gap-2">
              <div className="p-1 bg-green-100 rounded-lg">
                <ExternalLink className="h-4 w-4 text-green-600" />
              </div>
              {t('contentHistory.labels.dataSourceInfo', 'Data Source Information')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Article Title */}
            {task.summary_info.title && (
              <div className="space-y-2">
                <div className="text-sm font-medium text-green-800">
                  {t('contentHistory.labels.articleTitle', 'Article Title')}
                </div>
                <div className="p-3 bg-white/50 rounded-lg border border-green-100 text-sm font-medium text-foreground">
                  {task.summary_info.title}
                </div>
              </div>
            )}

            {/* Article Link */}
            {task.summary_info.url && (
              <div className="space-y-2">
                <div className="text-sm font-medium text-green-800">
                  {t('contentHistory.labels.articleLink', 'Article Link')}
                </div>
                <div className="p-3 bg-white/50 rounded-lg border border-green-100">
                  <a
                    href={task.summary_info.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-primary hover:text-primary/80 underline break-all flex items-center gap-2 transition-colors duration-300"
                  >
                    <ExternalLink className="h-4 w-4 flex-shrink-0" />
                    {task.summary_info.url}
                  </a>
                </div>
              </div>
            )}

            {/* Data Source Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-3 bg-white/50 rounded-lg border border-green-100">
                <div className="text-xs font-medium text-green-800 mb-1">
                  {t('contentHistory.labels.dataSource', 'Data Source')}
                </div>
                <div className="text-sm text-foreground font-medium">{task.summary_info.source_name}</div>
              </div>
              {task.summary_info.platform && (
                <div className="p-3 bg-white/50 rounded-lg border border-green-100">
                  <div className="text-xs font-medium text-green-800 mb-1">
                    {t('contentHistory.labels.dataSourcePlatform', 'Platform')}
                  </div>
                  <div className="text-sm text-foreground font-medium">{getPlatformName(task.summary_info.platform)}</div>
                </div>
              )}
              {task.summary_info.topic_name && (
                <div className="p-3 bg-white/50 rounded-lg border border-green-100">
                  <div className="text-xs font-medium text-green-800 mb-1">
                    {t('contentHistory.labels.dataSourceTopic', 'Topic')}
                  </div>
                  <div className="text-sm text-foreground font-medium">{task.summary_info.topic_name}</div>
                </div>
              )}
            </div>

            {/* Source Links Count */}
            {task.summary_info.source_urls && task.summary_info.source_urls.length > 0 && (
              <div className="p-3 bg-white/50 rounded-lg border border-green-100">
                <div className="text-xs font-medium text-green-800 mb-1">
                  {t('contentHistory.labels.sourceLinks', 'Source Links')}
                </div>
                <div className="text-sm text-muted-foreground">
                  {t('contentHistory.labels.sourceLinksCount', 'Contains {{count}} original links', { count: task.summary_info.source_urls.length })}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Error Information Section */}
      {task.status === 'failed' && task.error_message && (
        <Card className="bg-gradient-to-br from-red-50 to-red-100/50 border border-red-200/50 rounded-xl shadow-card">
          <CardHeader className="pb-4">
            <CardTitle className="text-sm font-semibold text-red-900 flex items-center gap-2">
              <div className="p-1 bg-red-100 rounded-lg">
                <AlertCircle className="h-4 w-4 text-red-600" />
              </div>
              {t('contentHistory.labels.errorMessage', 'Error Information')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="p-3 bg-white/50 rounded-lg border border-red-100 text-sm text-red-700">
              {task.error_message}
            </div>
            {task.retry_count && task.retry_count > 0 && (
              <div className="text-xs text-red-600 p-2 bg-red-50 rounded-lg border border-red-100">
                {t('contentHistory.labels.retryCount', 'Retry {{current}}/{{max}}', { current: task.retry_count, max: task.max_retries })}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Generated Content Section */}
      {task.status === 'completed' && task.generated_content && task.generated_content.length > 0 && (
        <Card className="bg-gradient-to-br from-purple-50 to-purple-100/50 border border-purple-200/50 rounded-xl shadow-card hover:shadow-glow transition-all duration-300">
          <CardHeader className="pb-4">
            <CardTitle className="text-sm font-semibold text-purple-900 flex items-center gap-2">
              <div className="p-1 bg-purple-100 rounded-lg">
                <FileText className="h-4 w-4 text-purple-600" />
              </div>
              {t('contentHistory.labels.generatedContent', 'Generated Content')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {task.generated_content.map((content) => (
              <div key={content.id} className="bg-white/70 border border-purple-100 rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300">
                <div className="flex items-center justify-between mb-3">
                  <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 transition-colors duration-300">
                    {getPlatformName(content.target_platform)}
                  </Badge>
                  <div className="flex items-center gap-2">
                    {onCopy && (
                      <Button
                        onClick={() => onCopy(content.content)}
                        variant="outline"
                        size="sm"
                        className="border-purple-200 text-purple-700 hover:bg-purple-50 hover:border-purple-300 transition-all duration-300"
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        {t('contentHistory.actions.copy', 'Copy')}
                      </Button>
                    )}
                    {onDelete && (
                      <Button
                        onClick={() => onDelete(content.id, task.id)}
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 hover:border-red-300 transition-all duration-300"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        {t('contentHistory.actions.delete', 'Delete')}
                      </Button>
                    )}
                  </div>
                </div>
                <div
                  className="text-sm leading-relaxed max-w-none markdown-content p-3 bg-white/50 rounded-lg border border-purple-100"
                  dangerouslySetInnerHTML={{
                    __html: renderMarkdown(content.content)
                  }}
                />
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TaskInfoCard;
