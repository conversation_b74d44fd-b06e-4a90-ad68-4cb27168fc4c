import React, { useState, useEffect, useMemo } from 'react';
import {
  Search,
  Filter,
  Copy,
  ExternalLink,
  Clock,
  Loader2,
  AlertCircle,
  FileText,
  X,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  XCircle,
  Pause,
  Play,
  RotateCcw,
  Trash2,
  BarChart3,
  ChevronDown,
  ChevronUp,
  Menu,
  Plus,
  Mail
} from 'lucide-react';
import TaskInfoCard from '@/components/TaskInfoCard';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Sheet, SheetContent, SheetTrigger, SheetTitle, SheetHeader } from '@/components/ui/sheet';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import { useIsMobile } from '@/hooks/use-mobile';
import { supabase } from '@/integrations/supabase/client';
import { renderMarkdown } from '@/utils/markdown';
import PlatformMultiSelect from '@/components/PlatformMultiSelect';

interface TaskStatus {
  task_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress?: number;
  queue_position?: number;
  estimated_completion?: string;
  result_ids?: string[];
  error_message?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  target_platforms: string[];
  style: string;
  user_input?: string;
  retry_count: number;
  max_retries: number;
  summary_info?: {
    id: string;
    content: string;
    platform: string;
    source_name: string;
    topic_name?: string;
    source_urls?: string[];
    title?: string;
    url?: string;
    created_at?: string;
  };
  generated_content?: Array<{
    id: string;
    target_platform: string;
    content: string;
    created_at: string;
  }>;
}



// These will be replaced with translation functions in the component

// Helper function to get platform from summary_type
const getPlatformFromSummaryType = (summaryType: string): string => {
  const typeMap: { [key: string]: string } = {
    'blog_post': 'blog',
    'daily_subreddit': 'reddit',
    'youtube_video': 'youtube',
    'podcast': 'podcast',
    'wechat_post': 'wechat',
    'twitter_rss_datasource': 'twitter-rss',
    'xiaohongshu_post': 'xiaohongshu'
  };
  return typeMap[summaryType] || 'unknown';
};

const UserContentHistory: React.FC = () => {
  const { t } = useTranslation();

  // Translation functions for platform and style names
  const getPlatformName = (platform: string): string => {
    const platformMap: { [key: string]: string } = {
      blog: t('platforms.blog', 'Blog'),
      linkedin: 'LinkedIn',
      twitter: 'Twitter',
      reddit: 'Reddit',
      xiaohongshu: t('platforms.xiaohongshu', 'Rednote'),
      wechat: t('platforms.wechat', 'WeChat'),
      youtube: 'YouTube',
      podcast: t('platforms.podcast', 'Podcast')
    };
    return platformMap[platform] || platform;
  };

  const getStyleName = (style: string): string => {
    const styleMap: { [key: string]: string } = {
      engaging: t('styles.engaging', 'Engaging'),
      professional: t('styles.professional', 'Professional'),
      casual: t('styles.casual', 'Casual'),
      creative: t('styles.creative', 'Creative'),
      analytical: t('styles.analytical', 'Analytical')
    };
    return styleMap[style] || style;
  };

  const [tasks, setTasks] = useState<TaskStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeSearchQuery, setActiveSearchQuery] = useState(''); // Actual search query for API calls
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sourcePlatformFilter, setSourcePlatformFilter] = useState<string[]>(['all']);
  const [targetPlatformFilter, setTargetPlatformFilter] = useState<string[]>(['all']);
  const [styleFilter, setStyleFilter] = useState<string>('all');
  const [topicFilter, setTopicFilter] = useState<string>('all');
  const [sourceFilter, setSourceFilter] = useState<string>('all');
  const [queueStats, setQueueStats] = useState<any>(null);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);
  const [topics, setTopics] = useState<Array<{id: string, name: string}>>([]);
  const [sources, setSources] = useState<Array<{name: string}>>([]);
  const [isFiltersCollapsed, setIsFiltersCollapsed] = useState(false);
  const [mobileFiltersOpen, setMobileFiltersOpen] = useState(false);
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());
  const { toast } = useToast();
  const { user } = useAuth();
  const isMobile = useIsMobile();

  // Toggle task expansion
  const toggleTaskExpansion = (taskId: string) => {
    console.log('Toggling task expansion for:', taskId);
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(taskId)) {
        newSet.delete(taskId);
        console.log('Collapsed task:', taskId);
      } else {
        newSet.add(taskId);
        console.log('Expanded task:', taskId);
      }
      console.log('Current expanded tasks:', Array.from(newSet));
      return newSet;
    });
  };

  // Calculate statistics
  const stats = useMemo(() => {
    return {
      totalTasks: tasks.length,
      pendingTasks: tasks.filter(t => t.status === 'pending').length,
      processingTasks: tasks.filter(t => t.status === 'processing').length,
      completedTasks: tasks.filter(t => t.status === 'completed').length,
      failedTasks: tasks.filter(t => t.status === 'failed').length,
      totalGeneratedContent: tasks.reduce((acc, task) =>
        acc + (task.generated_content?.length || 0), 0
      )
    };
  }, [tasks]);

  // Handle search submission
  const handleSearchSubmit = () => {
    setActiveSearchQuery(searchQuery);
  };

  // Handle enter key search
  const handleSearchKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearchSubmit();
    }
  };

  // Fetch topics list
  const fetchTopics = async () => {
    try {
      const { data, error } = await supabase
        .from('topics')
        .select('id, name')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      setTopics(data || []);
    } catch (err) {
      console.error('Error fetching topics:', err);
    }
  };

  // Fetch data sources list
  const fetchSources = async () => {
    try {
      // 从任务中提取唯一的数据源
      const uniqueSources = Array.from(
        new Set(
          tasks
            .filter(task => task.summary_info?.source_name)
            .map(task => task.summary_info!.source_name)
        )
      ).map(name => ({ name }));

      setSources(uniqueSources);
    } catch (err) {
      console.error('Error fetching sources:', err);
    }
  };

  useEffect(() => {
    if (user?.id) {
      fetchTasks();
      fetchTopics();
      // Set up polling to update task status
      const interval = setInterval(fetchTasks, 5000); // 每5秒更新一次
      setPollingInterval(interval);

      return () => {
        if (interval) clearInterval(interval);
      };
    }
  }, [user?.id, statusFilter, sourcePlatformFilter, targetPlatformFilter, styleFilter, topicFilter, sourceFilter, activeSearchQuery]);

  useEffect(() => {
    return () => {
      if (pollingInterval) clearInterval(pollingInterval);
    };
  }, [pollingInterval]);

  // Update data sources list when tasks are updated
  useEffect(() => {
    fetchSources();
  }, [tasks]);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if user is logged in
      if (!user?.id) {
        setError(t('contentHistory.errors.userNotLoggedIn'));
        setLoading(false);
        return;
      }

      // Query tasks and related information directly from database
      const { data: tasksData, error: tasksError } = await supabase
        .from('content_generation_queue')
        .select(`
          id,
          status,
          target_platforms,
          style,
          user_input,
          created_at,
          started_at,
          completed_at,
          result_ids,
          error_message,
          retry_count,
          max_retries,
          summary_id,
          summaries!inner(
            id,
            content,
            source_urls,
            metadata,
            summary_type,
            created_at
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(25);

      if (tasksError) throw tasksError;

      // Transform data format
      let filteredTasks: TaskStatus[] = (tasksData || []).map(task => {
        const summary = task.summaries as any;
        const metadata = summary?.metadata || {};

        // Intelligently get platform information using multiple fallback options
        const platform = metadata.platform ||
                         getPlatformFromSummaryType(summary?.summary_type) ||
                         'unknown';

        return {
          task_id: task.id,
          status: task.status,
          target_platforms: task.target_platforms,
          style: task.style,
          user_input: task.user_input,
          created_at: task.created_at,
          started_at: task.started_at,
          completed_at: task.completed_at,
          result_ids: task.result_ids,
          error_message: task.error_message,
          retry_count: task.retry_count || 0,
          max_retries: task.max_retries || 3,
          summary_info: summary ? {
            id: summary.id,
            content: summary.content,
            platform: platform,
            source_name: metadata.source_name || 'unknown',
            topic_name: metadata.topic_name,
            source_urls: summary.source_urls || [],
            title: metadata.post_title,
            url: metadata.post_url,
            created_at: summary.created_at
          } : undefined
        };
      });

      // Apply filters
      if (statusFilter !== 'all') {
        filteredTasks = filteredTasks.filter(task => task.status === statusFilter);
      }
      if (sourcePlatformFilter.length > 0 && !sourcePlatformFilter.includes('all')) {
        filteredTasks = filteredTasks.filter(task =>
          task.summary_info?.platform && sourcePlatformFilter.includes(task.summary_info.platform)
        );
      }
      if (targetPlatformFilter.length > 0 && !targetPlatformFilter.includes('all')) {
        filteredTasks = filteredTasks.filter(task =>
          task.target_platforms.some(platform => targetPlatformFilter.includes(platform))
        );
      }
      if (styleFilter !== 'all') {
        filteredTasks = filteredTasks.filter(task => task.style === styleFilter);
      }
      if (topicFilter !== 'all') {
        filteredTasks = filteredTasks.filter(task =>
          task.summary_info?.topic_name === topicFilter ||
          topics.find(t => t.id === topicFilter)?.name === task.summary_info?.topic_name
        );
      }
      if (sourceFilter !== 'all') {
        filteredTasks = filteredTasks.filter(task =>
          task.summary_info?.source_name === sourceFilter
        );
      }
      if (activeSearchQuery.trim()) {
        const query = activeSearchQuery.toLowerCase();
        filteredTasks = filteredTasks.filter(task =>
          task.summary_info?.content?.toLowerCase().includes(query) ||
          task.summary_info?.source_name?.toLowerCase().includes(query) ||
          task.summary_info?.title?.toLowerCase().includes(query) ||
          task.user_input?.toLowerCase().includes(query)
        );
      }

      // Get generated content for completed tasks
      for (const task of filteredTasks) {
        if (task.status === 'completed' && task.result_ids && task.result_ids.length > 0) {
          await fetchGeneratedContent(task);
        }
      }

      setTasks(filteredTasks);

      // Calculate queue statistics
      const stats = {
        total_pending: filteredTasks.filter(t => t.status === 'pending').length,
        total_processing: filteredTasks.filter(t => t.status === 'processing').length,
        total_completed: filteredTasks.filter(t => t.status === 'completed').length,
        total_failed: filteredTasks.filter(t => t.status === 'failed').length,
        estimated_wait_time_minutes: filteredTasks.filter(t => t.status === 'pending').length * 2
      };
      setQueueStats(stats);

    } catch (error: any) {
      console.error('Error fetching tasks:', error);
      setError(error.message || t('contentHistory.loading'));
    } finally {
      setLoading(false);
    }
  };

  const fetchGeneratedContent = async (task: TaskStatus) => {
    try {
      const { data, error } = await supabase
        .from('user_generated_content')
        .select('id, target_platform, content, created_at')
        .in('id', task.result_ids || []);

      if (!error && data) {
        task.generated_content = data;
      }
    } catch (error) {
      console.warn('Failed to fetch generated content for task:', task.task_id, error);
    }
  };



  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: t('contentHistory.actions.copied'),
        description: t('contentHistory.actions.copiedDesc'),
      });
    } catch (error) {
      toast({
        title: t('contentHistory.actions.copyFailed'),
        description: t('contentHistory.actions.copyFailedDesc'),
        variant: 'destructive',
      });
    }
  };

  const deleteContent = async (contentId: string, taskId: string) => {
    // Show confirmation dialog
    if (!confirm(t('contentHistory.actions.deleteConfirm'))) {
      return;
    }

    try {
      // Delete from user_generated_content table
      const { error: deleteError } = await supabase
        .from('user_generated_content')
        .delete()
        .eq('id', contentId);

      if (deleteError) throw deleteError;

      // Find the task and update its result_ids
      const { data: taskData, error: taskFetchError } = await supabase
        .from('content_generation_queue')
        .select('result_ids')
        .eq('id', taskId)
        .single();

      if (taskFetchError) {
        console.error('Error fetching task data:', taskFetchError);
      } else if (taskData) {
        const currentResultIds = taskData.result_ids || [];
        const updatedResultIds = currentResultIds.filter((id: string) => id !== contentId);

        if (updatedResultIds.length === 0) {
          // If no more content, delete the entire task
          const { error: taskDeleteError } = await supabase
            .from('content_generation_queue')
            .delete()
            .eq('id', taskId);

          if (taskDeleteError) {
            console.error('Error deleting task:', taskDeleteError);
          }
        } else {
          // Update the task with remaining result_ids
          const { error: taskUpdateError } = await supabase
            .from('content_generation_queue')
            .update({ result_ids: updatedResultIds })
            .eq('id', taskId);

          if (taskUpdateError) {
            console.error('Error updating task result_ids:', taskUpdateError);
          }
        }
      }

      toast({
        title: t('contentHistory.actions.deleteSuccess'),
        description: t('contentHistory.actions.deleteSuccessDesc'),
      });

      // Update local state
      setTasks(prevTasks => {
        return prevTasks.map(task => {
          if (task.id === taskId && task.generated_content) {
            const updatedContent = task.generated_content.filter(content => content.id !== contentId);

            // If no more content, we should remove this task from the list
            // But since we're filtering, we'll return null and filter it out
            if (updatedContent.length === 0) {
              return null;
            }

            return {
              ...task,
              generated_content: updatedContent
            };
          }
          return task;
        }).filter(task => task !== null) as TaskStatus[];
      });

    } catch (error: any) {
      console.error('Error deleting content:', error);
      toast({
        title: t('contentHistory.actions.deleteFailed'),
        description: error.message || t('contentHistory.actions.deleteFailedDesc'),
        variant: 'destructive',
      });
    }
  };

  const clearFilters = () => {
    setSearchQuery('');
    setActiveSearchQuery('');
    setStatusFilter('all');
    setSourcePlatformFilter(['all']);
    setTargetPlatformFilter(['all']);
    setStyleFilter('all');
    setTopicFilter('all');
    setSourceFilter('all');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'processing':
        return <Play className="h-4 w-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <Pause className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return t('contentHistory.status.pending');
      case 'processing':
        return t('contentHistory.status.processing');
      case 'completed':
        return t('contentHistory.status.completed');
      case 'failed':
        return t('contentHistory.status.failed');
      case 'cancelled':
        return t('contentHistory.status.cancelled');
      default:
        return status;
    }
  };

  if (loading && tasks.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-muted-foreground">{t('contentHistory.loading')}</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">{t('contentHistory.errors.loadFailed')}</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={fetchTasks}>{t('contentHistory.actions.retry')}</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container-wide mx-auto mobile-content-area mobile-ultra-compact responsive-padding py-8 lg:py-12 space-4xl">
      {/* Header - Enhanced with better spacing and visual hierarchy */}
      <div className="flex flex-col gap-6 lg:gap-8 mb-12 lg:mb-16 space-3xl">
        {/* Main row - Title and Stats Cards */}
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-6 lg:gap-8">
          {/* Left side - Title and Buttons */}
          <div className="flex flex-col gap-4 lg:gap-6 space-lg">
            <h1 className="text-heading-1 text-responsive-2xl font-bold mobile-title text-gradient blur-fade-in">
              {t('contentHistory.title')}
            </h1>
          </div>

          {/* Right side - Enhanced Stats Cards */}
          <div className="flex flex-wrap gap-4 lg:gap-6 sm:items-end bounce-in">
            {/* Total Tasks Card */}
            <div className="flex items-center gap-4 px-6 py-4 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl min-w-0 flex-1 sm:flex-none card-3d group hover:bg-gradient-card-hover shadow-card hover:shadow-glow transition-all duration-300">
              <div className="p-3 bg-gradient-primary rounded-xl shadow-card group-hover:shadow-glow group-hover:scale-110 transition-all duration-300 transform-gpu">
                <FileText className="h-6 w-6 text-primary-foreground group-hover:animate-pulse" />
              </div>
              <div className="text-center min-w-0">
                <div className="text-2xl lg:text-3xl font-bold text-gradient truncate group-hover:scale-105 transition-transform duration-300">
                  {stats.totalTasks.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground truncate group-hover:text-foreground transition-colors duration-300">{t('contentHistory.stats.totalTasks')}</div>
              </div>
            </div>

            {/* Completed Tasks Card */}
            <div className="flex items-center gap-4 px-6 py-4 bg-gradient-to-br from-green-50 to-green-100/50 rounded-xl min-w-0 flex-1 sm:flex-none card-3d group hover:bg-gradient-card-hover shadow-card hover:shadow-glow transition-all duration-300" style={{ animationDelay: '0.1s' }}>
              <div className="p-3 bg-gradient-primary rounded-xl shadow-card group-hover:shadow-glow group-hover:scale-110 transition-all duration-300 transform-gpu">
                <CheckCircle className="h-6 w-6 text-primary-foreground group-hover:animate-bounce" />
              </div>
              <div className="text-center min-w-0">
                <div className="text-2xl lg:text-3xl font-bold text-gradient truncate group-hover:scale-105 transition-transform duration-300">
                  {stats.completedTasks.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground truncate group-hover:text-foreground transition-colors duration-300">{t('contentHistory.stats.completedTasks')}</div>
              </div>
            </div>

            {/* Generated Content Card */}
            <div className="flex items-center gap-4 px-6 py-4 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl min-w-0 flex-1 sm:flex-none card-3d group hover:bg-gradient-card-hover shadow-card hover:shadow-glow transition-all duration-300" style={{ animationDelay: '0.2s' }}>
              <div className="p-3 bg-gradient-primary rounded-xl shadow-card group-hover:shadow-glow group-hover:scale-110 transition-all duration-300 transform-gpu">
                <BarChart3 className="h-6 w-6 text-primary-foreground group-hover:animate-pulse" />
              </div>
              <div className="text-center min-w-0">
                <div className="text-2xl lg:text-3xl font-bold text-gradient truncate group-hover:scale-105 transition-transform duration-300">
                  {stats.totalGeneratedContent.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground truncate group-hover:text-foreground transition-colors duration-300">{t('contentHistory.stats.generatedContent')}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters - Enhanced with glass morphism */}
      <Card className="mobile-title mobile-content-ultra mobile-border-expand mb-8 lg:mb-12 glass-effect border-border/50 shadow-card hover:shadow-glow transition-all duration-300 scale-in">
        <CardHeader className="pb-4 space-md">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg lg:text-xl font-medium flex items-center gap-3">
              <div className="p-2 bg-gradient-primary rounded-lg shadow-card">
                <Filter className="h-5 w-5 text-primary-foreground" />
              </div>
              {t('contentHistory.filters.filterAndSearch')}
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsFiltersCollapsed(!isFiltersCollapsed)}
              className="h-8 w-8 p-0 hover:bg-primary/10 hover:scale-110 transition-all duration-300"
            >
              {isFiltersCollapsed ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronUp className="h-4 w-4" />
              )}
            </Button>
          </div>
        </CardHeader>

        {!isFiltersCollapsed && (
          <CardContent className="mobile-filters mobile-content-ultra pt-0 space-lg">
            <div className="mobile-form-spacing space-y-6">
              {/* Enhanced Search Box */}
              <div className="relative group">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5 group-focus-within:text-primary transition-colors duration-300" />
                <Input
                  placeholder={t('contentHistory.filters.searchPlaceholder')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={handleSearchKeyPress}
                  className="pl-12 pr-24 h-12 text-base border-border/50 focus:border-primary/50 focus:ring-primary/20 transition-all duration-300 bg-background/50 backdrop-blur"
                />
                <Button
                  onClick={handleSearchSubmit}
                  size="sm"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 bg-primary hover:bg-primary/90 shadow-card hover:shadow-glow transition-all duration-300"
                >
                  {t('contentHistory.filters.search')}
                </Button>
              </div>

              {/* Enhanced Filter Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">{t('contentHistory.filters.status')}</Label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="h-11 border-border/50 focus:border-primary/50 focus:ring-primary/20 transition-all duration-300 bg-background/50 backdrop-blur">
                      <SelectValue placeholder={t('contentHistory.filters.allStatuses')} />
                    </SelectTrigger>
                    <SelectContent className="border-border/50 bg-background/95 backdrop-blur">
                      <SelectItem value="all">{t('contentHistory.filters.allStatuses')}</SelectItem>
                      <SelectItem value="pending">{t('contentHistory.status.pending')}</SelectItem>
                      <SelectItem value="processing">{t('contentHistory.status.processing')}</SelectItem>
                      <SelectItem value="completed">{t('contentHistory.status.completed')}</SelectItem>
                      <SelectItem value="failed">{t('contentHistory.status.failed')}</SelectItem>
                      <SelectItem value="cancelled">{t('contentHistory.status.cancelled')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">{t('contentHistory.filters.targetPlatform')}</Label>
                  <PlatformMultiSelect
                    selectedPlatforms={targetPlatformFilter}
                    onPlatformChange={setTargetPlatformFilter}
                    placeholder={t('contentHistory.filters.allPlatforms')}
                    allPlatformsText={t('contentHistory.filters.allPlatforms')}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">{t('contentHistory.filters.style')}</Label>
                  <Select value={styleFilter} onValueChange={setStyleFilter}>
                    <SelectTrigger className="h-11 border-border/50 focus:border-primary/50 focus:ring-primary/20 transition-all duration-300 bg-background/50 backdrop-blur">
                      <SelectValue placeholder={t('contentHistory.filters.allStyles')} />
                    </SelectTrigger>
                    <SelectContent className="border-border/50 bg-background/95 backdrop-blur">
                      <SelectItem value="all">{t('contentHistory.filters.allStyles')}</SelectItem>
                      {['engaging', 'professional', 'casual', 'creative', 'analytical'].map((key) => (
                        <SelectItem key={key} value={key}>{getStyleName(key)}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">{t('contentHistory.filters.platform')}</Label>
                  <PlatformMultiSelect
                    selectedPlatforms={sourcePlatformFilter}
                    onPlatformChange={setSourcePlatformFilter}
                    placeholder={t('contentHistory.filters.allPlatforms')}
                    allPlatformsText={t('contentHistory.filters.allPlatforms')}
                  />
                </div>
              </div>

              {/* Enhanced Second Filter Row */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">{t('contentHistory.filters.topic')}</Label>
                  <Select value={topicFilter} onValueChange={setTopicFilter}>
                    <SelectTrigger className="h-11 border-border/50 focus:border-primary/50 focus:ring-primary/20 transition-all duration-300 bg-background/50 backdrop-blur">
                      <SelectValue placeholder={t('contentHistory.filters.allTopics')} />
                    </SelectTrigger>
                    <SelectContent className="border-border/50 bg-background/95 backdrop-blur">
                      <SelectItem value="all">{t('contentHistory.filters.allTopics')}</SelectItem>
                      {topics.map(topic => (
                        <SelectItem key={topic.id} value={topic.id}>
                          {topic.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">{t('contentHistory.filters.dataSource')}</Label>
                  <Select value={sourceFilter} onValueChange={setSourceFilter}>
                    <SelectTrigger className="h-11 border-border/50 focus:border-primary/50 focus:ring-primary/20 transition-all duration-300 bg-background/50 backdrop-blur">
                      <SelectValue placeholder={t('contentHistory.filters.allDataSources')} />
                    </SelectTrigger>
                    <SelectContent className="border-border/50 bg-background/95 backdrop-blur">
                      <SelectItem value="all">{t('contentHistory.filters.allDataSources')}</SelectItem>
                      {sources.map(source => (
                        <SelectItem key={source.name} value={source.name}>
                          {source.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button onClick={clearFilters} variant="outline" className="flex items-center gap-2 w-full h-11 border-border/50 hover:bg-accent/50 hover:border-primary/50 transition-all duration-300">
                    <X className="h-4 w-4" />
                    {t('contentHistory.filters.clearFilters')}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Enhanced Task List */}
      <Card className="glass-effect border-border/50 shadow-card hover:shadow-glow transition-all duration-300 card-3d">
        <CardHeader className="space-md">
          <CardTitle className="flex items-center gap-3 text-lg">
            <div className="p-2 bg-gradient-primary rounded-lg shadow-card">
              <FileText className="h-5 w-5 text-primary-foreground" />
            </div>
            <div className="flex flex-col">
              <span>{t('contentHistory.taskList')}</span>
              <Badge variant="secondary" className="w-fit mt-1">
                {tasks.length}
              </Badge>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="mobile-content-ultra">
          <div className="space-y-4">
            {/* Content List */}
            <div className="mobile-card-spacing space-y-4">
              {tasks.length > 0 ? (
                tasks.map((task) => (
                  <Card
                    key={task.task_id}
                    className="glass-effect border-border/50 shadow-card hover:shadow-glow transition-all duration-300 card-3d group hover:bg-gradient-card-hover cursor-pointer"
                    onClick={(e) => {
                      console.log('Card clicked!', task.task_id);
                      toggleTaskExpansion(task.task_id);
                    }}
                  >
                    <CardContent className="p-6 space-lg">
                      {/* Click indicator */}
                      <div className="text-xs text-muted-foreground mb-2">
                        点击展开/折叠 (状态: {expandedTasks.has(task.task_id) ? '已展开' : '已折叠'})
                      </div>

                      {/* Enhanced Top Badge Row */}
                      <div className="flex items-center gap-2 mb-4 flex-wrap">
                        {getStatusIcon(task.status)}
                        <Badge variant={task.status === 'completed' ? 'default' : 'secondary'} className="text-xs px-3 py-1 bg-primary/10 text-primary border-primary/30 hover:bg-primary/20 transition-colors duration-300">
                          {getStatusText(task.status)}
                        </Badge>
                        <Badge variant="secondary" className="text-xs px-3 py-1 bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 transition-colors duration-300">
                          {getStyleName(task.style)}
                        </Badge>
                        {task.summary_info?.platform && (
                          <Badge variant="outline" className="text-xs px-3 py-1 bg-green-50 text-green-700 border-green-200 hover:bg-green-100 transition-colors duration-300">
                            {getPlatformName(task.summary_info.platform)}
                          </Badge>
                        )}
                        {task.summary_info?.topic_name && (
                          <Badge variant="outline" className="text-xs px-3 py-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 transition-colors duration-300">
                            {task.summary_info.topic_name}
                          </Badge>
                        )}
                      </div>

                      {/* Enhanced Title */}
                      <h3 className="text-lg font-semibold text-foreground mb-4 leading-tight group-hover:text-primary transition-colors duration-300">
                        {task.summary_info?.title || t('contentHistory.labels.generationTask')}
                      </h3>

                      {/* Enhanced Bottom Information Row */}
                      <div className="flex items-center justify-between mb-6 p-4 bg-muted/30 rounded-lg border border-border/30">
                        {/* Left Side: Source, Time and Platform */}
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span className="font-medium text-foreground">
                            {task.summary_info?.source_name || t('contentHistory.labels.unknown')}
                          </span>
                          <span className="flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            {new Date(task.created_at).toLocaleDateString()}
                          </span>
                          {task.summary_info?.platform && (
                            <span className="text-sm font-medium text-foreground">
                              {getPlatformName(task.summary_info.platform)}
                            </span>
                          )}
                        </div>

                        {/* Right Side: Article Link */}
                        {task.summary_info?.url && (
                          <a
                            href={task.summary_info.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-2 text-sm text-primary hover:text-primary/80 underline transition-colors duration-300"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <ExternalLink className="h-4 w-4" />
                            {t('contentHistory.labels.articleLink', 'Article Link')}
                          </a>
                        )}
                      </div>

                      {/* Enhanced Task Information using TaskInfoCard */}
                      <TaskInfoCard
                        task={task}
                        onCopy={copyToClipboard}
                        onDelete={deleteContent}
                        isExpanded={expandedTasks.has(task.task_id)}
                      />
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="text-center py-16 px-8">
                  <div className="p-4 bg-muted/30 rounded-full w-fit mx-auto mb-6">
                    <FileText className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-foreground">{t('contentHistory.empty.title')}</h3>
                  <p className="text-muted-foreground text-lg max-w-md mx-auto">
                    {t('contentHistory.empty.description')}
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserContentHistory;
